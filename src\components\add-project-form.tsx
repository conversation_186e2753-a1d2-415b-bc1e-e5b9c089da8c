"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';
import { toast } from 'sonner';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FileUpload } from '@/components/file-upload';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { createProject, Project } from '@/services/project-service';
import {
  Building,
  Calendar,
  FileText,
  Image,
  MapPin,
  DollarSign,
  ClipboardList,
  CheckCircle2
} from 'lucide-react';

// Define the form input types
interface AddProjectFormInputs {
  // Project Overview
  name: string;
  location: string;
  type: string;
  status: string;
  completion: string;
  description: string;

  // Building & Compliance
  buildingConsent: string;
  resourceConsent: string;
  topoStart: string;
  topoCompleted: string;
  epa: string;
  worksOver: string;
  worksOverNumber: string;

  // Timeline & Budget
  startDate: string;
  completionDate: string;
  estimatedBudget: string;
  actualCost: string;
  salePrice: string;
  lender: string;

  // Additional Details
  existingDwellings: string;
  newDwellings: string;
  clientName: string;
  projectManager: string;
}

interface AddProjectFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddProjectForm({ open, onOpenChange }: AddProjectFormProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [documents, setDocuments] = useState<File[]>([]);
  const [photos, setPhotos] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<AddProjectFormInputs>({
    defaultValues: {
      name: '',
      location: '',
      type: '',
      status: 'Planning',
      completion: '',
      description: '',
      buildingConsent: '',
      resourceConsent: '',
      topoStart: '',
      topoCompleted: '',
      epa: '',
      worksOver: 'Not Applicable',
      worksOverNumber: '',
      startDate: new Date().toISOString().split('T')[0],
      completionDate: '',
      estimatedBudget: '',
      actualCost: '',
      salePrice: '',
      lender: '',
      existingDwellings: '0',
      newDwellings: '0',
      clientName: '',
      projectManager: '',
    },
    criteriaMode: "all",
    mode: "onChange",
  });

  const onSubmit: SubmitHandler<AddProjectFormInputs> = async (data) => {
    try {
      setIsSubmitting(true);

      // Validate required fields
      if (!data.name || !data.location || !data.type || !data.status) {
        toast.error('Please fill in all required fields');
        setIsSubmitting(false);

        // Set focus to the first empty required field
        if (!data.name) {
          form.setFocus('name');
          setActiveTab('overview');
        } else if (!data.location) {
          form.setFocus('location');
          setActiveTab('overview');
        } else if (!data.type) {
          form.setFocus('type');
          setActiveTab('overview');
        } else if (!data.status) {
          form.setFocus('status');
          setActiveTab('overview');
        }

        return;
      }

      // Format completion date for display
      let formattedCompletionDate = '';
      if (data.completionDate) {
        const date = new Date(data.completionDate);
        formattedCompletionDate = date.toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric'
        });
      }

      // Create project object with snake_case property names for Supabase
      const projectData: Project = {
        name: data.name.trim(),
        location: data.location.trim(),
        type: data.type,
        status: data.status,
        completion: formattedCompletionDate || undefined,
        description: data.description ? data.description.trim() : undefined,
        // Convert camelCase to snake_case for database columns
        building_consent: data.buildingConsent ? data.buildingConsent.trim() : undefined,
        resource_consent: data.resourceConsent ? data.resourceConsent.trim() : undefined,
        topo_start: data.topoStart || undefined,
        topo_completed: data.topoCompleted || undefined,
        epa: data.epa ? data.epa.trim() : undefined,
        works_over: data.worksOver || undefined,
        works_over_number: data.worksOverNumber ? data.worksOverNumber.trim() : undefined,
        start_date: data.startDate || undefined,
        completion_date: data.completionDate || undefined,
        estimated_budget: data.estimatedBudget ? data.estimatedBudget.trim() : undefined,
        actual_cost: data.actualCost ? data.actualCost.trim() : undefined,
        sale_price: data.salePrice ? data.salePrice.trim() : undefined,
        lender: data.lender ? data.lender.trim() : undefined,
        existing_dwellings: data.existingDwellings || undefined,
        new_dwellings: data.newDwellings || undefined,
        client_name: data.clientName ? data.clientName.trim() : undefined,
        project_manager: data.projectManager ? data.projectManager.trim() : undefined,
      };

      console.log('Submitting project data:', projectData);

      // Save project to Supabase
      const { data: newProject, error } = await createProject(projectData);

      if (error) {
        console.error('Detailed error from Supabase:', error);
        let errorMessage = 'Failed to create project';

        if (error.message) {
          errorMessage = error.message;
        } else if (error.details && error.details.message) {
          errorMessage = error.details.message;
        }

        if (errorMessage.includes('does not exist')) {
          errorMessage = 'The projects table does not exist in the database. Please create it in the Supabase dashboard or visit the /diagnostic page for more information.';
        } else if (errorMessage.includes('column')) {
          errorMessage = 'There is a mismatch between the application and database schema. Please check the console for details, update the database schema in Supabase, or visit the /diagnostic page for more information.';
        } else {
          // For any other error, suggest using the diagnostic page
          errorMessage = `${errorMessage} - Please visit the /diagnostic page for more information.`;
        }

        toast.error(errorMessage);
        return;
      }

      // Handle file uploads (in a real app, you would upload these to storage)
      console.log('Documents to upload:', documents);
      console.log('Photos to upload:', photos);

      // Close the dialog
      onOpenChange(false);

      // Show success message
      toast.success('Project created successfully!');

      // Refresh the projects list
      router.refresh();

      // Add a small delay before redirecting to ensure the router refresh has time to work
      setTimeout(() => {
        // Redirect to the new project page if we have an ID
        if (newProject?.id) {
          console.log('Redirecting to project page:', newProject.id);
          router.push(`/projects/${newProject.id}`);
        } else {
          // If we don't have an ID, just go to the projects page
          console.log('Redirecting to projects page');
          router.push('/projects');
        }
      }, 500);
    } catch (error: any) {
      console.error('Error creating project:', error);
      let errorMessage = 'Failed to create project. Please try again.';

      if (error && error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDocumentsUpload = (files: File[]) => {
    setDocuments(files);
  };

  const handlePhotosUpload = (files: File[]) => {
    setPhotos(files);
  };

  const navigateToNextTab = () => {
    if (activeTab === 'overview') setActiveTab('compliance');
    else if (activeTab === 'compliance') setActiveTab('timeline');
    else if (activeTab === 'timeline') setActiveTab('details');
    else if (activeTab === 'details') setActiveTab('uploads');
  };

  const navigateToPrevTab = () => {
    if (activeTab === 'uploads') setActiveTab('details');
    else if (activeTab === 'details') setActiveTab('timeline');
    else if (activeTab === 'timeline') setActiveTab('compliance');
    else if (activeTab === 'compliance') setActiveTab('overview');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto bg-white">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-[#0271c3]">Add New Project</DialogTitle>
          <DialogDescription className="text-sm">
            Fill in the project details. You can skip optional sections if needed.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="relative">
                <TabsList className="grid grid-cols-5 mb-0 bg-transparent p-0 space-x-1">
                  <TabsTrigger
                    value="overview"
                    className="flex items-center gap-2
                    data-[state=active]:bg-white data-[state=active]:text-[#0260a8] data-[state=active]:font-semibold
                    px-4 py-2 rounded-t-md font-medium text-gray-500 hover:text-[#0260a8] transition-all duration-200
                    border border-gray-300
                    data-[state=inactive]:bg-gray-200 data-[state=inactive]:border-gray-300
                    data-[state=active]:border-b-0 data-[state=active]:relative data-[state=active]:z-20"
                  >
                    <ClipboardList className="h-4 w-4" />
                    <span className="hidden sm:inline">Overview</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="compliance"
                    className="flex items-center gap-2
                    data-[state=active]:bg-white data-[state=active]:text-[#0260a8] data-[state=active]:font-semibold
                    px-4 py-2 rounded-t-md font-medium text-gray-500 hover:text-[#0260a8] transition-all duration-200
                    border border-gray-300
                    data-[state=inactive]:bg-gray-200 data-[state=inactive]:border-gray-300
                    data-[state=active]:border-b-0 data-[state=active]:relative data-[state=active]:z-20"
                  >
                    <Building className="h-4 w-4" />
                    <span className="hidden sm:inline">Compliance</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="timeline"
                    className="flex items-center gap-2
                    data-[state=active]:bg-white data-[state=active]:text-[#0260a8] data-[state=active]:font-semibold
                    px-4 py-2 rounded-t-md font-medium text-gray-500 hover:text-[#0260a8] transition-all duration-200
                    border border-gray-300
                    data-[state=inactive]:bg-gray-200 data-[state=inactive]:border-gray-300
                    data-[state=active]:border-b-0 data-[state=active]:relative data-[state=active]:z-20"
                  >
                    <Calendar className="h-4 w-4" />
                    <span className="hidden sm:inline">Timeline</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="details"
                    className="flex items-center gap-2
                    data-[state=active]:bg-white data-[state=active]:text-[#0260a8] data-[state=active]:font-semibold
                    px-4 py-2 rounded-t-md font-medium text-gray-500 hover:text-[#0260a8] transition-all duration-200
                    border border-gray-300
                    data-[state=inactive]:bg-gray-200 data-[state=inactive]:border-gray-300
                    data-[state=active]:border-b-0 data-[state=active]:relative data-[state=active]:z-20"
                  >
                    <MapPin className="h-4 w-4" />
                    <span className="hidden sm:inline">Details</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="uploads"
                    className="flex items-center gap-2
                    data-[state=active]:bg-white data-[state=active]:text-[#0260a8] data-[state=active]:font-semibold
                    px-4 py-2 rounded-t-md font-medium text-gray-500 hover:text-[#0260a8] transition-all duration-200
                    border border-gray-300
                    data-[state=inactive]:bg-gray-200 data-[state=inactive]:border-gray-300
                    data-[state=active]:border-b-0 data-[state=active]:relative data-[state=active]:z-20"
                  >
                    <FileText className="h-4 w-4" />
                    <span className="hidden sm:inline">Uploads</span>
                  </TabsTrigger>
                </TabsList>
                <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gray-300 z-10"></div>
              </div>

              {/* Project Overview Tab */}
              <TabsContent value="overview" className="space-y-3 bg-white border border-slate-400 border-t-0 rounded-b-md rounded-tr-md p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="name"
                    rules={{
                      required: "Project name is required",
                      minLength: {
                        value: 3,
                        message: "Project name must be at least 3 characters"
                      }
                    }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Name*</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter project name" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location"
                    rules={{
                      required: "Location is required"
                    }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location*</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter project location" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    rules={{
                      required: "Project type is required"
                    }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Type*</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <SelectValue placeholder="Select project type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            <SelectItem value="Single-family">Single-family</SelectItem>
                            <SelectItem value="Multi-family">Multi-family</SelectItem>
                            <SelectItem value="Townhouse">Townhouse</SelectItem>
                            <SelectItem value="Commercial">Commercial</SelectItem>
                            <SelectItem value="Luxury Homes">Luxury Homes</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status*</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            <SelectItem value="Planning">Planning</SelectItem>
                            <SelectItem value="In Progress">In Progress</SelectItem>
                            <SelectItem value="Completed">Completed</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter project description"
                          className="min-h-[100px] bg-white"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end">
                  <Button type="button" onClick={navigateToNextTab}>
                    Next: Building & Compliance
                  </Button>
                </div>
              </TabsContent>

              {/* Building & Compliance Tab */}
              <TabsContent value="compliance" className="space-y-3 bg-white border border-slate-400 border-t-0 rounded-b-md rounded-tr-md p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="buildingConsent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Building Consent #</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter building consent number" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="resourceConsent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Resource Consent #</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter resource consent number" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="topoStart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Topo Start</FormLabel>
                        <FormControl>
                          <Input type="date" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="topoCompleted"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Topo Completed</FormLabel>
                        <FormControl>
                          <Input type="date" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="epa"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>EPA</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter EPA information" className="bg-white" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="worksOver"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Works Over</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            <SelectItem value="Not Applicable">Not Applicable</SelectItem>
                            <SelectItem value="Applicable">Applicable</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('worksOver') === 'Applicable' && (
                    <FormField
                      control={form.control}
                      name="worksOverNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Works Over Number</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter works over number" className="bg-white" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={navigateToPrevTab}>
                    Back
                  </Button>
                  <Button type="button" onClick={navigateToNextTab}>
                    Next: Timeline & Budget
                  </Button>
                </div>
              </TabsContent>

              {/* Timeline & Budget Tab */}
              <TabsContent value="timeline" className="space-y-4 bg-white border border-gray-300 border-t-0 rounded-b-md rounded-tr-md p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date</FormLabel>
                        <FormControl>
                          <Input type="date" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="completionDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Completion Date</FormLabel>
                        <FormControl>
                          <Input type="date" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="estimatedBudget"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated Budget</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. $250,000" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="actualCost"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Actual Cost</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. $0" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="salePrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Sale Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="e.g. 500000"
                            className="bg-white"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lender</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter lender name" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={navigateToPrevTab}>
                    Back
                  </Button>
                  <Button type="button" onClick={navigateToNextTab}>
                    Next: Additional Details
                  </Button>
                </div>
              </TabsContent>

              {/* Additional Details Tab */}
              <TabsContent value="details" className="space-y-4 bg-white border border-gray-300 border-t-0 rounded-b-md rounded-tr-md p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="existingDwellings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Existing Dwellings</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            <SelectItem value="0">0</SelectItem>
                            <SelectItem value="1">1</SelectItem>
                            <SelectItem value="2">2</SelectItem>
                            <SelectItem value="3">3</SelectItem>
                            <SelectItem value="4+">4+</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="newDwellings"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of New Dwellings</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            <SelectItem value="0">0</SelectItem>
                            <SelectItem value="1">1</SelectItem>
                            <SelectItem value="2">2</SelectItem>
                            <SelectItem value="3">3</SelectItem>
                            <SelectItem value="4+">4+</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="clientName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Client Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter client name" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="projectManager"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Manager</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter project manager" className="bg-white" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={navigateToPrevTab}>
                    Back
                  </Button>
                  <Button type="button" onClick={navigateToNextTab}>
                    Next: Upload Files
                  </Button>
                </div>
              </TabsContent>

              {/* Uploads Tab */}
              <TabsContent value="uploads" className="space-y-6 bg-white border border-gray-300 border-t-0 rounded-b-md rounded-tr-md p-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Upload Documents</h3>
                  <FileUpload
                    onUpload={handleDocumentsUpload}
                    acceptedFileTypes="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    multiple={true}
                    maxFiles={10}
                    maxSize={10}
                    buttonText="Upload Documents"
                    buttonIcon={<FileText className="h-4 w-4 mr-1" />}
                    buttonClassName="bg-[#0271c3] hover:bg-[#0271c3]/90 text-white"
                  />

                  {documents.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium mb-2">{documents.length} document(s) selected:</p>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {documents.map((doc, index) => (
                          <li key={index} className="flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-blue-600" />
                            {doc.name}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Upload Photos</h3>
                  <FileUpload
                    onUpload={handlePhotosUpload}
                    acceptedFileTypes="image/*"
                    multiple={true}
                    maxFiles={10}
                    maxSize={5}
                    buttonText="Upload Photos"
                    buttonIcon={<Image className="h-4 w-4 mr-1" />}
                    buttonClassName="bg-[#0271c3] hover:bg-[#0271c3]/90 text-white"
                  />

                  {photos.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium mb-2">{photos.length} photo(s) selected:</p>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                        {photos.map((photo, index) => (
                          <div key={index} className="relative aspect-square rounded-md overflow-hidden border border-gray-200">
                            <img
                              src={URL.createObjectURL(photo)}
                              alt={`Preview ${index}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={navigateToPrevTab}>
                    Back
                  </Button>
                  <Button
                    type="submit"
                    className="bg-[#0271c3] hover:bg-[#0271c3]/90"
                    disabled={isSubmitting}
                  >
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    {isSubmitting ? 'Creating...' : 'Create Project'}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </form>
        </Form>

        <DialogFooter className="flex justify-between items-center border-t pt-4">
          <div className="text-sm text-gray-500">
            * Required fields
          </div>
          <div className="text-sm text-gray-500">
            {isSubmitting ? 'Creating project...' : 'You can skip optional sections'}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
