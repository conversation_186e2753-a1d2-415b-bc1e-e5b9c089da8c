'use client';

import { useState, useEffect } from 'react';
import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { NewProjectButton } from '@/components/new-project-button';
import { PhotoPreview } from '@/components/photo-preview';
import { FolderTree, FolderItem } from '@/components/folder-tree';
import { FileUpload } from '@/components/file-upload';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Upload, Download, Trash2, Share2, Filter, FolderPlus } from 'lucide-react';
import { getPhotos, uploadPhoto, deletePhoto, Photo } from '@/services/photo-service';
import { toast } from 'sonner';

export default function PhotosPage() {
  // Photo state
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // State for selected photo and preview
  const [selectedPhotoId, setSelectedPhotoId] = useState<string | null>(null);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);

  // Folder state
  const [folders, setFolders] = useState<FolderItem[]>([
    {
      id: 'folder-1',
      name: 'Site Photos',
      type: 'folder',
      children: [
        {
          id: 'folder-1-1',
          name: 'Before Construction',
          type: 'folder',
          parentId: 'folder-1',
          children: []
        },
        {
          id: 'folder-1-2',
          name: 'During Construction',
          type: 'folder',
          parentId: 'folder-1',
          children: []
        },
        {
          id: 'folder-1-3',
          name: 'After Construction',
          type: 'folder',
          parentId: 'folder-1',
          children: []
        }
      ]
    },
    {
      id: 'folder-2',
      name: 'Progress Photos',
      type: 'folder',
      children: []
    },
    {
      id: 'folder-3',
      name: 'Inspection Photos',
      type: 'folder',
      children: []
    }
  ]);

  // Get the selected photo for preview
  const selectedPhoto = selectedPhotoId ? photos.find(photo => photo.id === selectedPhotoId) || null : null;

  // Get photos for sharing
  const photosForSharing = photos.filter(photo => selectedPhotos.includes(photo.id));

  // Folder management functions
  const createFolder = (parentId: string | null, name: string) => {
    const newFolder: FolderItem = {
      id: `folder-${Date.now()}`,
      name,
      type: 'folder',
      parentId: parentId || undefined,
      children: []
    };

    if (parentId) {
      // Add to parent folder
      setFolders(prev => {
        const updateFolders = (items: FolderItem[]): FolderItem[] => {
          return items.map(item => {
            if (item.id === parentId) {
              return {
                ...item,
                children: [...(item.children || []), newFolder]
              };
            } else if (item.children) {
              return {
                ...item,
                children: updateFolders(item.children)
              };
            }
            return item;
          });
        };
        return updateFolders(prev);
      });
    } else {
      // Add to root
      setFolders(prev => [...prev, newFolder]);
    }

    toast.success(`Folder "${name}" created successfully`);
  };

  const renameItem = (itemId: string, newName: string) => {
    setFolders(prev => {
      const updateFolders = (items: FolderItem[]): FolderItem[] => {
        return items.map(item => {
          if (item.id === itemId) {
            return { ...item, name: newName };
          } else if (item.children) {
            return {
              ...item,
              children: updateFolders(item.children)
            };
          }
          return item;
        });
      };
      return updateFolders(prev);
    });

    toast.success(`Folder renamed to "${newName}"`);
  };

  const deleteItem = (itemId: string) => {
    setFolders(prev => {
      const removeFromFolders = (items: FolderItem[]): FolderItem[] => {
        return items.filter(item => {
          if (item.id === itemId) {
            return false;
          } else if (item.children) {
            item.children = removeFromFolders(item.children);
          }
          return true;
        });
      };
      return removeFromFolders(prev);
    });

    // Clear selection if the deleted folder was selected
    if (currentFolderId === itemId) {
      setCurrentFolderId(null);
    }

    toast.success('Folder deleted successfully');
  };

  const handleFolderClick = (item: FolderItem) => {
    if (item.type === 'folder') {
      setCurrentFolderId(item.id);
    }
  };

  // Fetch photos from Supabase
  const fetchPhotos = async () => {
    try {
      setLoading(true);
      const { data, error } = await getPhotos();

      if (error) {
        console.error('Error fetching photos:', error);
        setError('Failed to load photos. Please try again later.');
        return;
      }

      if (data) {
        setPhotos(data);
      }

      setError(null);
    } catch (err) {
      console.error('Unexpected error:', err);
      setError('An unexpected error occurred. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch photos when the component mounts
  useEffect(() => {
    fetchPhotos();
  }, []);

  // Handle photo upload
  const handlePhotoUpload = async (files: File[]) => {
    try {
      setLoading(true);

      const uploadPromises = files.map(file => uploadPhoto(file));
      const results = await Promise.all(uploadPromises);

      const newPhotos = results
        .filter(result => result.data !== null)
        .map(result => result.data as Photo);

      if (newPhotos.length > 0) {
        setPhotos(prev => [...prev, ...newPhotos]);

        // Automatically select the first uploaded photo for preview
        setSelectedPhotoId(newPhotos[0].id);

        // Show success message
        toast.success(`Successfully uploaded ${newPhotos.length} photo${newPhotos.length > 1 ? 's' : ''}`);
      }

      // Show error if some files failed to upload
      const failedCount = files.length - newPhotos.length;
      if (failedCount > 0) {
        toast.error(`Failed to upload ${failedCount} photo${failedCount > 1 ? 's' : ''}`);
      }
    } catch (err) {
      console.error('Error uploading photos:', err);
      toast.error('An error occurred while uploading photos');
    } finally {
      setLoading(false);
    }
  };

  // Handle photo selection for preview
  const handlePhotoClick = (photoId: string) => {
    setSelectedPhotoId(photoId);
  };

  // Handle photo selection for actions (sharing, etc.)
  const handlePhotoSelect = (photoId: string, isChecked: boolean) => {
    if (isChecked) {
      setSelectedPhotos(prev => [...prev, photoId]);
    } else {
      setSelectedPhotos(prev => prev.filter(id => id !== photoId));
    }
  };

  // Handle select all photos
  const handleSelectAll = (isChecked: boolean) => {
    if (isChecked) {
      setSelectedPhotos(photos.map(photo => photo.id));
    } else {
      setSelectedPhotos([]);
    }
  };

  // Filter photos based on search term
  const filteredPhotos = photos.filter(photo =>
    photo.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
        <div className="ml-60 mt-[72px] w-full h-screen flex overflow-hidden">
          {/* Folder Tree Sidebar */}
          <div className="w-64 p-4 border-r bg-gray-50 overflow-auto">
            <FolderTree
              items={folders}
              onItemClick={handleFolderClick}
              onCreateFolder={createFolder}
              onRenameItem={renameItem}
              onDeleteItem={deleteItem}
              selectedItemId={currentFolderId}
            />
          </div>

          {/* Photo List Section */}
          <div className="flex-1 md:w-1/2 lg:w-2/5 p-4 overflow-auto border-r">
            <div className="mb-4 flex justify-between items-center">
              <div>
                <h1 className="text-xl font-bold text-blue-700">Photos</h1>
                <p className="text-gray-600 mt-0.5 text-sm">Project photo documentation (JPEG/PNG only)</p>
              </div>
            </div>

            <div className="mb-4 flex flex-col sm:flex-row gap-2 justify-between items-start sm:items-center">
              <div className="relative w-full sm:w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search photos..."
                  className="pl-9 pr-4 py-2 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex gap-2 w-full sm:w-auto">
                <Button variant="outline" size="sm" className="flex-shrink-0">
                  <Filter className="h-4 w-4 mr-1" />
                  Filter
                </Button>
                <FileUpload
                  onUpload={handlePhotoUpload}
                  acceptedFileTypes=".jpg,.jpeg,.png"
                  multiple={true}
                  maxFiles={10}
                  maxSize={5}
                  buttonText="Upload Photos"
                  buttonIcon={<Upload className="h-4 w-4 mr-1" />}
                  buttonClassName="bg-blue-600 hover:bg-blue-700 flex-shrink-0 ml-auto sm:ml-0"
                />
              </div>
            </div>

            {selectedPhotos.length > 0 && (
              <div className="mb-4 flex items-center justify-between bg-blue-50 p-3 rounded-md">
                <span className="text-sm font-medium text-blue-700">
                  {selectedPhotos.length} {selectedPhotos.length === 1 ? 'photo' : 'photos'} selected
                </span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-blue-600"
                  >
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                  <Button variant="outline" size="sm" className="text-blue-600">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600"
                    onClick={async () => {
                      if (confirm('Are you sure you want to delete the selected photos? This action cannot be undone.')) {
                        try {
                          setLoading(true);

                          // Get the photos to delete
                          const photosToDelete = photos.filter(photo => selectedPhotos.includes(photo.id));

                          // Delete each photo
                          const deletePromises = photosToDelete.map(photo =>
                            deletePhoto(photo.id, photo.file_path || '')
                          );

                          await Promise.all(deletePromises);

                          // Remove deleted photos from state
                          setPhotos(prev => prev.filter(photo => !selectedPhotos.includes(photo.id)));
                          setSelectedPhotos([]);

                          // If the selected photo was deleted, clear the selection
                          if (selectedPhotoId && selectedPhotos.includes(selectedPhotoId)) {
                            setSelectedPhotoId(null);
                          }

                          toast.success(`Successfully deleted ${photosToDelete.length} photo(s)`);
                        } catch (err) {
                          console.error('Error deleting photos:', err);
                          toast.error('An error occurred while deleting photos');
                        } finally {
                          setLoading(false);
                        }
                      }
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            )}

            <div className="bg-white rounded-lg shadow-md border border-slate-400 overflow-hidden">
              {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading photos...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <p className="text-red-600 mb-2">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchPhotos()}
              >
                Try Again
              </Button>
            </div>
          ) : photos.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-600 mb-2">No photos found</p>
              <p className="text-sm text-gray-500">Upload photos using the button above</p>
            </div>
          ) : (
                <div className="p-4">
                  <div className="mb-4 flex items-center">
                    <Checkbox
                      id="select-all-photos"
                      checked={selectedPhotos.length === photos.length}
                      onCheckedChange={handleSelectAll}
                      className="mr-2"
                    />
                    <label htmlFor="select-all-photos" className="text-sm font-medium text-gray-700">
                      Select All ({filteredPhotos.length} photos)
                    </label>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {filteredPhotos.map((photo) => (
                      <div
                        key={photo.id}
                        className={`border border-slate-400 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer ${
                          selectedPhotoId === photo.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => handlePhotoClick(photo.id)}
                      >
                        <div className="relative">
                          <div className="absolute top-2 left-2 z-10">
                            <Checkbox
                              checked={selectedPhotos.includes(photo.id)}
                              onCheckedChange={(checked) => {
                                handlePhotoSelect(photo.id, checked as boolean);
                              }}
                              onClick={(e) => e.stopPropagation()}
                              className="bg-white border-2"
                            />
                          </div>
                          <div className="h-40 overflow-hidden bg-gray-100 flex items-center justify-center">
                            {photo.thumbnail ? (
                              <img
                                src={photo.thumbnail}
                                alt={photo.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  console.error('Error loading image:', photo.thumbnail);
                                  const imgElement = e.currentTarget as HTMLImageElement;
                                  imgElement.src = `https://placehold.co/300x200/e2e8f0/1e40af?text=${encodeURIComponent(photo.title)}`;
                                }}
                              />
                            ) : (
                              <div className="text-center p-4">
                                <p className="text-gray-500">Image not available</p>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="p-3">
                          <h3 className="font-medium text-sm">{photo.title}</h3>
                          <p className="text-xs text-gray-500 mt-0.5">{photo.date}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Photo Preview Section */}
          <div className="flex-1 p-4 pb-20 overflow-auto bg-[#f9fdff]">
            <div className="mb-4 flex justify-between items-center">
              <div>
                <h2 className="text-lg font-semibold text-blue-700">Photo Preview</h2>
              </div>
              <div>
                <NewProjectButton />
              </div>
            </div>

            <PhotoPreview
              photo={selectedPhoto}
              onShare={(photo) => {
                setSelectedPhotos([photo.id]);
                // Add share functionality here if needed
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}




