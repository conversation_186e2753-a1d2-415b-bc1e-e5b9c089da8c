'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  LayoutGrid,
  BarChart3,
  Calendar,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProjectsList } from '@/components/projects-list';
import { Dashboard } from '@/components/dashboard';
import { NewProjectButton } from '@/components/new-project-button';
// UI components are used in parent components

interface ProjectsTabsProps {
  projects: any[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  showFilters: boolean;
  filters: {
    status: string;
    type: string;
  };
  onFiltersChange: (filters: { status: string; type: string; }) => void;
  onToggleFilters: () => void;
}

export function ProjectsTabs({
  projects,
  searchTerm,
  onSearchChange,
  showFilters,
  filters,
  onFiltersChange,
  onToggleFilters
}: ProjectsTabsProps) {
  const [activeTab, setActiveTab] = useState('projects');

  // Filter projects based on search term and filters
  const filteredProjects = projects.filter(project => {
    // Search term filter
    const matchesSearch = searchTerm === '' ||
      (project.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.location?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.type?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.description?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.client_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.project_manager?.toLowerCase() || '').includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = filters.status === '' || project.status === filters.status;

    // Type filter
    const matchesType = filters.type === '' || project.type === filters.type;

    // Return true only if all conditions are met
    return matchesSearch && matchesStatus && matchesType;
  });

  // Filter projects for "For Sale" tab - only show projects with "For Sale" status
  const forSaleProjects = projects.filter(project => {
    const isForSale = project.status === 'For Sale';

    // Apply search filter to for sale projects
    const matchesSearch = searchTerm === '' ||
      (project.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.location?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.type?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.description?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.client_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (project.project_manager?.toLowerCase() || '').includes(searchTerm.toLowerCase());

    return isForSale && matchesSearch;
  });

  const tabs = [
    { id: 'projects', label: 'Projects', icon: <LayoutGrid className="h-5 w-5" /> },
    { id: 'dashboard', label: 'Dashboard', icon: <BarChart3 className="h-5 w-5" /> },
    { id: 'for-sale', label: 'For Sale', icon: <LayoutGrid className="h-5 w-5" /> },
    { id: 'calendar', label: 'Calendar', icon: <Calendar className="h-5 w-5" /> },
  ];

  return (
    <div className="w-full">
      {/* Modern Tab Navigation with New Project Button */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
        <div className="flex justify-between border-b">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex items-center gap-2 px-6 py-4 font-medium text-sm transition-all duration-200 border-b-2",
                  activeTab === tab.id
                    ? "text-blue-600 border-blue-600"
                    : "text-gray-500 border-transparent hover:text-blue-500 hover:border-blue-200"
                )}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </div>
          <div className="flex items-center pr-4">
            <NewProjectButton />
          </div>
        </div>

        {/* Tab Content with Animation */}
        <div className="p-6">
          {/* Projects Tab */}
          {activeTab === 'projects' && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Search results count */}
              {searchTerm && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-3 mb-6">
                  <div className="text-sm text-gray-500">
                    Found {filteredProjects.length} {filteredProjects.length === 1 ? 'project' : 'projects'} matching &quot;{searchTerm}&quot;
                  </div>
                </div>
              )}

              {/* Filters panel */}
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="bg-white p-5 rounded-xl border border-gray-200 mb-6 shadow-sm"
                >
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-800">Filter Projects</h3>
                    <button
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                      onClick={() => onToggleFilters()}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Project Status</label>
                      <div className="flex flex-wrap gap-2">
                        <button
                          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            !filters.status
                              ? 'bg-blue-100 text-blue-800 border border-blue-200'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-transparent'
                          }`}
                          onClick={() => onFiltersChange({...filters, status: ''})}
                        >
                          All
                        </button>
                        <button
                          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            filters.status === 'Planning'
                              ? 'bg-blue-100 text-blue-800 border border-blue-200'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-transparent'
                          }`}
                          onClick={() => onFiltersChange({...filters, status: 'Planning'})}
                        >
                          Planning
                        </button>
                        <button
                          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            filters.status === 'In Progress'
                              ? 'bg-blue-100 text-blue-800 border border-blue-200'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-transparent'
                          }`}
                          onClick={() => onFiltersChange({...filters, status: 'In Progress'})}
                        >
                          In Progress
                        </button>
                        <button
                          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            filters.status === 'Completed'
                              ? 'bg-blue-100 text-blue-800 border border-blue-200'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-transparent'
                          }`}
                          onClick={() => onFiltersChange({...filters, status: 'Completed'})}
                        >
                          Completed
                        </button>
                        <button
                          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            filters.status === 'For Sale'
                              ? 'bg-blue-100 text-blue-800 border border-blue-200'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-transparent'
                          }`}
                          onClick={() => onFiltersChange({...filters, status: 'For Sale'})}
                        >
                          For Sale
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Project Type</label>
                      <select
                        className="w-full rounded-lg border-gray-200 shadow-sm py-2.5 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        value={filters.type}
                        onChange={(e) => onFiltersChange({...filters, type: e.target.value})}
                      >
                        <option value="">All Types</option>
                        <option value="Standalone">Standalone</option>
                        <option value="Terraced House">Terraced House</option>
                        <option value="Apartments">Apartments</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex justify-end mt-6 pt-4 border-t border-gray-100">
                    <button
                      className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium mr-2"
                      onClick={() => onFiltersChange({ status: '', type: '' })}
                    >
                      Reset Filters
                    </button>
                    <button
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                      onClick={() => onToggleFilters()}
                    >
                      Apply Filters
                    </button>
                  </div>
                </motion.div>
              )}

              <ProjectsList
                projects={filteredProjects}
                searchTerm={searchTerm}
                onSearchChange={onSearchChange}
                showFilters={showFilters}
                filters={filters}
                onFiltersChange={onFiltersChange}
                onToggleFilters={onToggleFilters}
              />

              {filteredProjects.length === 0 && (
                <div className="text-center py-12">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 text-blue-500 mb-4">
                    <Search className="h-8 w-8" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                  <p className="text-gray-500 max-w-md mx-auto mb-6">
                    We couldn&apos;t find any projects matching your search criteria. Try adjusting your search or create a new project.
                  </p>
                  <NewProjectButton />
                </div>
              )}
            </motion.div>
          )}

          {/* Dashboard Tab */}
          {activeTab === 'dashboard' && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Dashboard projects={filteredProjects} />
            </motion.div>
          )}

          {/* For Sale Tab */}
          {activeTab === 'for-sale' && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <ProjectsList
                projects={forSaleProjects}
                searchTerm={searchTerm}
                onSearchChange={onSearchChange}
                showFilters={showFilters}
                filters={filters}
                onFiltersChange={onFiltersChange}
                onToggleFilters={onToggleFilters}
              />

              {forSaleProjects.length === 0 && (
                <div className="text-center py-12">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 text-blue-500 mb-4">
                    <LayoutGrid className="h-8 w-8" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No properties for sale</h3>
                  <p className="text-gray-500 max-w-md mx-auto mb-6">
                    There are currently no projects with "For Sale" status. Update a project's status to "For Sale" to see it here.
                  </p>
                  <NewProjectButton />
                </div>
              )}
            </motion.div>
          )}

          {/* Calendar Tab */}
          {activeTab === 'calendar' && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="min-h-[400px] flex items-center justify-center"
            >
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 text-blue-500 mb-4">
                  <Calendar className="h-8 w-8" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Calendar View Coming Soon</h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  We&apos;re working on a calendar view to help you visualize project timelines and deadlines.
                </p>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}
