'use client';

import { useState, useEffect } from 'react';
import { AppHeader } from '@/components/app-header';
import { ProjectsTabs } from '@/components/projects-tabs';
// Mock project interface
interface Project {
  id?: string;
  name: string;
  location: string;
  type: string;
  status: string;
  completion?: string;
  description?: string;
  building_consent?: string;
  resource_consent?: string;
  topo_start?: string;
  topo_completed?: string;
  epa?: string;
  works_over?: string;
  works_over_number?: string;
  start_date?: string;
  completion_date?: string;
  estimated_budget?: string;
  actual_cost?: string;
  sale_price?: string;
  lender?: string;
  existing_dwellings?: string;
  new_dwellings?: string;
  client_name?: string;
  project_manager?: string;
  created_at?: string;
  updated_at?: string;
}
import { Skeleton } from '@/components/ui/skeleton';

// Fallback sample project data in case API fails
const sampleProjects = [
  {
    id: 1,
    name: 'Dreadon Road Development',
    location: '33b Dreadon Road, Manurewa',
    type: 'Single-family',
    status: 'In Progress',
    completion: 'December 15, 2025',
    description: 'A modern single-family home development project located in Manurewa. The project includes a 3-bedroom house with modern amenities and sustainable features.'
  },
  {
    id: 2,
    name: 'Oakridge Apartments',
    location: '45 Oakridge Blvd, Auckland',
    type: 'Multi-family',
    status: 'Planning',
    completion: 'June 30, 2026',
    description: 'A luxury apartment complex with 24 units featuring modern designs and premium amenities in a prime Auckland location.'
  }
];

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    type: ''
  });

  // Mock function to simulate fetching projects
  const fetchProjects = async () => {
    try {
      setLoading(true);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Use sample data as mock data
      setProjects(sampleProjects as unknown as Project[]);
      setError(null);
    } catch (err) {
      setError('An unexpected error occurred. Please try again later.');
      setProjects(sampleProjects as unknown as Project[]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch projects when the component mounts
  useEffect(() => {
    fetchProjects();
  }, []);

  return (
    <div className="flex flex-col h-screen">
      <AppHeader hideSearch={true} />
      <div className="flex-1 mt-[72px] p-6 pb-24 h-screen overflow-y-auto bg-[#f9fdff]">
        <div className="max-w-7xl mx-auto">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          ) : error ? (
            <div className="p-4 bg-red-50 text-red-700 rounded-md space-y-4">
              <p>{error}</p>
              <p className="text-sm">
                Having trouble with Supabase? Visit the{' '}
                <a href="/diagnostic" className="text-blue-600 underline hover:text-blue-800">
                  diagnostic page
                </a>{' '}
                for troubleshooting.
              </p>
            </div>
          ) : (
            <ProjectsTabs
              projects={projects}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              showFilters={showFilters}
              filters={filters}
              onFiltersChange={setFilters}
              onToggleFilters={() => setShowFilters(!showFilters)}
            />
          )}
        </div>
      </div>
    </div>
  );
}






