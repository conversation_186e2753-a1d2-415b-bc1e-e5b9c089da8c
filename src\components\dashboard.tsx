'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

interface DashboardProps {
  projects: any[];
}

export function Dashboard({ projects }: DashboardProps) {
  // Calculate project statistics
  const validProjects = Array.isArray(projects) ? projects : [];
  const totalProjects = validProjects.length;
  const inProgressProjects = validProjects.filter(p => p.status === 'In Progress').length;
  const planningProjects = validProjects.filter(p => p.status === 'Planning').length;
  const completedProjects = validProjects.filter(p => p.status === 'Completed').length;
  const onHoldProjects = validProjects.filter(p => p.status === 'On Hold').length;
  const forSaleProjects = validProjects.filter(p => p.status === 'For Sale').length;

  return (
    <div className="space-y-4 pt-2 mb-8">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="pb-1">
            <CardTitle className="text-xs font-medium text-muted-foreground">Total Projects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="bg-blue-100 p-1.5 rounded-lg mr-2.5">
                <BuildingIcon className="w-4 h-4 text-blue-600" />
              </div>
              <div className="text-xl font-bold">{totalProjects}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-1">
            <CardTitle className="text-xs font-medium text-muted-foreground">In Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="bg-blue-100 p-1.5 rounded-lg mr-2.5">
                <ProgressIcon className="w-4 h-4 text-blue-600" />
              </div>
              <div className="text-xl font-bold text-blue-600">{inProgressProjects}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-1">
            <CardTitle className="text-xs font-medium text-muted-foreground">Planning</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="bg-yellow-100 p-1.5 rounded-lg mr-2.5">
                <PlanningIcon className="w-4 h-4 text-yellow-600" />
              </div>
              <div className="text-xl font-bold text-yellow-600">{planningProjects}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-1">
            <CardTitle className="text-xs font-medium text-muted-foreground">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="bg-emerald-100 p-1.5 rounded-lg mr-2.5">
                <CheckIcon className="w-4 h-4 text-emerald-600" />
              </div>
              <div className="text-xl font-bold text-emerald-600">{completedProjects}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-1">
            <CardTitle className="text-xs font-medium text-muted-foreground">On Hold</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="bg-orange-100 p-1.5 rounded-lg mr-2.5">
                <PauseIcon className="w-4 h-4 text-orange-600" />
              </div>
              <div className="text-xl font-bold text-orange-600">{onHoldProjects}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-1">
            <CardTitle className="text-xs font-medium text-muted-foreground">For Sale</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="bg-green-100 p-1.5 rounded-lg mr-2.5">
                <SaleIcon className="w-4 h-4 text-green-600" />
              </div>
              <div className="text-xl font-bold text-green-600">{forSaleProjects}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Project Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[160px] flex items-center justify-center">
              <div className="flex gap-2">
                <div className="flex flex-col items-center">
                  <div className="w-12 bg-blue-500 rounded-t-lg" style={{ height: `${totalProjects > 0 ? (inProgressProjects / totalProjects) * 120 : 0}px`, minHeight: '4px' }}></div>
                  <p className="text-xs mt-1">Progress</p>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 bg-yellow-500 rounded-t-lg" style={{ height: `${totalProjects > 0 ? (planningProjects / totalProjects) * 120 : 0}px`, minHeight: '4px' }}></div>
                  <p className="text-xs mt-1">Planning</p>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 bg-emerald-500 rounded-t-lg" style={{ height: `${totalProjects > 0 ? (completedProjects / totalProjects) * 120 : 0}px`, minHeight: '4px' }}></div>
                  <p className="text-xs mt-1">Complete</p>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 bg-orange-500 rounded-t-lg" style={{ height: `${totalProjects > 0 ? (onHoldProjects / totalProjects) * 120 : 0}px`, minHeight: '4px' }}></div>
                  <p className="text-xs mt-1">On Hold</p>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 bg-green-500 rounded-t-lg" style={{ height: `${totalProjects > 0 ? (forSaleProjects / totalProjects) * 120 : 0}px`, minHeight: '4px' }}></div>
                  <p className="text-xs mt-1">For Sale</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <ActivityIcon className="w-3.5 h-3.5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Project updated</p>
                  <p className="text-xs text-muted-foreground">Dreadon Road Development - Status changed to In Progress</p>
                  <p className="text-xs text-muted-foreground mt-0.5">2 hours ago</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-green-100 p-1.5 rounded-lg">
                  <ActivityIcon className="w-3.5 h-3.5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Document uploaded</p>
                  <p className="text-xs text-muted-foreground">Riverside Townhouses - Building permit added</p>
                  <p className="text-xs text-muted-foreground mt-0.5">Yesterday</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="bg-yellow-100 p-1.5 rounded-lg">
                  <ActivityIcon className="w-3.5 h-3.5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">New project created</p>
                  <p className="text-xs text-muted-foreground">Mountain View Residences added to projects</p>
                  <p className="text-xs text-muted-foreground mt-0.5">3 days ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Project Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-200"></div>

            {validProjects.slice(0, 3).map((project, index) => (
              <div key={project.id} className="relative pl-8 pb-6">
                <div className="absolute left-0 top-1 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-blue-600 text-xs font-bold">{index + 1}</span>
                </div>
                <div>
                  <h3 className="text-sm font-semibold">{project.name}</h3>
                  <p className="text-xs text-muted-foreground mt-0.5">Completion: {project.completion}</p>
                  <div className="mt-1.5 h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div
                      className={`h-full rounded-full ${
                        project.status === 'In Progress' ? 'bg-blue-500 w-1/2' :
                        project.status === 'Planning' ? 'bg-yellow-500 w-1/4' :
                        project.status === 'On Hold' ? 'bg-orange-500 w-1/3' :
                        project.status === 'For Sale' ? 'bg-green-500 w-3/4' :
                        project.status === 'Completed' ? 'bg-emerald-500 w-full' :
                        'bg-gray-500 w-1/4'
                      }`}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Building icon component
function BuildingIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect>
      <path d="M9 22v-4h6v4"></path>
      <path d="M8 6h.01"></path>
      <path d="M16 6h.01"></path>
      <path d="M12 6h.01"></path>
      <path d="M12 10h.01"></path>
      <path d="M12 14h.01"></path>
      <path d="M16 10h.01"></path>
      <path d="M16 14h.01"></path>
      <path d="M8 10h.01"></path>
      <path d="M8 14h.01"></path>
    </svg>
  );
}

// Progress icon component
function ProgressIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M18 20V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14"></path>
      <path d="M2 20h20"></path>
      <path d="M14 12v.01"></path>
      <path d="M14 16v.01"></path>
      <path d="M10 12v.01"></path>
      <path d="M10 16v.01"></path>
    </svg>
  );
}

// Planning icon component
function PlanningIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
      <path d="M14 2v6h6"></path>
      <path d="M16 13H8"></path>
      <path d="M16 17H8"></path>
      <path d="M10 9H8"></path>
    </svg>
  );
}

// Check icon component
function CheckIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
      <polyline points="22 4 12 14.01 9 11.01"></polyline>
    </svg>
  );
}

// Activity icon component
function ActivityIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
    </svg>
  );
}

// Pause icon component
function PauseIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <rect x="6" y="4" width="4" height="16"></rect>
      <rect x="14" y="4" width="4" height="16"></rect>
    </svg>
  );
}

// Sale icon component
function SaleIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <circle cx="9" cy="21" r="1"></circle>
      <circle cx="20" cy="21" r="1"></circle>
      <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
    </svg>
  );
}
