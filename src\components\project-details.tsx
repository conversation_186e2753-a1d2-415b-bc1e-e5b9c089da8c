import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export function ProjectDetails() {
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dreadon Road Development</h1>
        <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm font-medium">
          In Progress
        </div>
      </div>

      <h2 className="text-xl font-semibold text-blue-800 mb-4">Project Details</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="project-name">Project Name</Label>
            <Input 
              id="project-name" 
              value="Dreadon Road Development" 
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="project-type">Project Type</Label>
            <Input 
              id="project-type" 
              value="Single-family" 
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="existing-dwellings">Number of Existing Dwellings</Label>
            <Select defaultValue="0">
              <SelectTrigger id="existing-dwellings" className="mt-1">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">0</SelectItem>
                <SelectItem value="1">1</SelectItem>
                <SelectItem value="2">2</SelectItem>
                <SelectItem value="3">3</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="client-name">Client Name</Label>
            <Input 
              id="client-name" 
              placeholder="Enter client name" 
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="estimated-completion">Estimated Completion</Label>
            <Input 
              id="estimated-completion" 
              type="date" 
              className="mt-1"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="location">Location</Label>
            <Input 
              id="location" 
              value="33b Dreadon Road, Manurewa" 
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="project-status">Project Status</Label>
            <Select defaultValue="in-progress">
              <SelectTrigger id="project-status" className="mt-1">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Planning">Planning</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="On Hold">On Hold</SelectItem>
                <SelectItem value="For Sale">For Sale</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="new-dwellings">Number of New Dwellings</Label>
            <Select defaultValue="0">
              <SelectTrigger id="new-dwellings" className="mt-1">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">0</SelectItem>
                <SelectItem value="1">1</SelectItem>
                <SelectItem value="2">2</SelectItem>
                <SelectItem value="3">3</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="project-manager">Project Manager</Label>
            <Input 
              id="project-manager" 
              placeholder="Enter project manager" 
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Input 
              id="description" 
              value="A modern single-family home development project." 
              className="mt-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
