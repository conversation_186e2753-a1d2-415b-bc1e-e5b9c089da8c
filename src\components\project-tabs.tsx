'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ClipboardList,
  Building,
  Calendar,
  Users,
  FileText,
  Image,
  CheckCircle2,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileUpload } from '@/components/file-upload';
import { toast } from 'sonner';

interface ProjectTabsProps {
  project?: any;
  isEditing?: boolean;
}

export function ProjectTabs({ project, isEditing = true }: ProjectTabsProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Form state
  const [projectName, setProjectName] = useState(project?.name || '');
  const [projectLocation, setProjectLocation] = useState(project?.location || '');
  const [projectType, setProjectType] = useState(project?.type || '');
  const [projectStatus, setProjectStatus] = useState(project?.status || 'Planning');
  const [projectDescription, setProjectDescription] = useState(project?.description || '');

  // Building & Compliance state
  const [buildingConsent, setBuildingConsent] = useState('');
  const [resourceConsent, setResourceConsent] = useState('');
  const [worksOver, setWorksOver] = useState('Not Applicable');
  const [worksOverNumber, setWorksOverNumber] = useState('');

  // Timeline & Budget state
  const [startDate, setStartDate] = useState('');
  const [completionDate, setCompletionDate] = useState('');
  const [estimatedBudget, setEstimatedBudget] = useState('');
  const [actualCost, setActualCost] = useState('');
  const [salePrice, setSalePrice] = useState('');

  // Team state
  const [clientName, setClientName] = useState('');
  const [projectManager, setProjectManager] = useState('');

  // Files state
  const [documents, setDocuments] = useState<File[]>([]);
  const [photos, setPhotos] = useState<File[]>([]);

  const tabs = [
    { id: 'overview', label: 'Overview', icon: <ClipboardList className="h-5 w-5" /> },
    { id: 'building', label: 'Building & Compliance', icon: <Building className="h-5 w-5" /> },
    { id: 'timeline', label: 'Timeline & Budget', icon: <Calendar className="h-5 w-5" /> },
    { id: 'team', label: 'Team', icon: <Users className="h-5 w-5" /> },
    { id: 'files', label: 'Files', icon: <FileText className="h-5 w-5" /> },
  ];

  const handleStatusChange = (newStatus: string) => {
    setProjectStatus(newStatus);
    setHasUnsavedChanges(true);

    // Show save confirmation toast
    toast.success(`Status changed to "${newStatus}". Don't forget to save your changes!`, {
      action: {
        label: 'Save Now',
        onClick: () => handleSave()
      },
      duration: 5000
    });
  };

  const handleSave = () => {
    // Save logic would go here
    console.log('Saving project data...');
    setHasUnsavedChanges(false);
    toast.success('Project saved successfully!');
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
      {/* Modern Tab Navigation */}
      <div className="flex overflow-x-auto scrollbar-hide">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={cn(
              "flex items-center gap-2 px-6 py-4 font-medium text-sm whitespace-nowrap transition-all duration-200 border-b-2 cursor-pointer",
              activeTab === tab.id
                ? "text-blue-600 border-blue-600 bg-blue-50/50"
                : "text-gray-500 border-transparent hover:text-blue-500 hover:bg-blue-50/30"
            )}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content with Animation */}
      <div className="p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="project-name" className="text-gray-700">Project Name</Label>
                <Input
                  id="project-name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="Enter project name"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="project-location" className="text-gray-700">Location</Label>
                <Input
                  id="project-location"
                  value={projectLocation}
                  onChange={(e) => setProjectLocation(e.target.value)}
                  placeholder="Enter project location"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="project-type" className="text-gray-700">Project Type</Label>
                <Select
                  value={projectType}
                  onValueChange={setProjectType}
                  disabled={!isEditing}
                >
                  <SelectTrigger id="project-type" className="mt-1.5">
                    <SelectValue placeholder="Select project type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Standalone">Standalone</SelectItem>
                    <SelectItem value="Terraced House">Terraced House</SelectItem>
                    <SelectItem value="Apartments">Apartments</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="project-status" className="text-gray-700">Status</Label>
                <Select
                  value={projectStatus}
                  onValueChange={handleStatusChange}
                  disabled={!isEditing}
                >
                  <SelectTrigger id="project-status" className="mt-1.5">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Planning">Planning</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                    <SelectItem value="On Hold">On Hold</SelectItem>
                    <SelectItem value="For Sale">For Sale</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="project-description" className="text-gray-700">Description</Label>
              <Textarea
                id="project-description"
                value={projectDescription}
                onChange={(e) => setProjectDescription(e.target.value)}
                placeholder="Enter project description"
                className="mt-1.5 min-h-[120px]"
                disabled={!isEditing}
              />
            </div>

            {isEditing && (
              <div className="flex justify-end">
                <Button
                  onClick={() => setActiveTab('building')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Next: Building & Compliance
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            )}
          </motion.div>
        )}

        {/* Building & Compliance Tab */}
        {activeTab === 'building' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Building & Compliance form fields would go here */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="building-consent" className="text-gray-700">Building Consent</Label>
                <Input
                  id="building-consent"
                  value={buildingConsent}
                  onChange={(e) => setBuildingConsent(e.target.value)}
                  placeholder="Enter building consent details"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="resource-consent" className="text-gray-700">Resource Consent</Label>
                <Input
                  id="resource-consent"
                  value={resourceConsent}
                  onChange={(e) => setResourceConsent(e.target.value)}
                  placeholder="Enter resource consent details"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="works-over" className="text-gray-700">Works Over</Label>
                <Select
                  value={worksOver}
                  onValueChange={setWorksOver}
                  disabled={!isEditing}
                >
                  <SelectTrigger id="works-over" className="mt-1.5">
                    <SelectValue placeholder="Select option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Not Applicable">Not Applicable</SelectItem>
                    <SelectItem value="Applicable">Applicable</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {worksOver === 'Applicable' && (
                <div>
                  <Label htmlFor="works-over-number" className="text-gray-700">Works Over Number</Label>
                  <Input
                    id="works-over-number"
                    value={worksOverNumber}
                    onChange={(e) => setWorksOverNumber(e.target.value)}
                    placeholder="Enter works over number"
                    className="mt-1.5"
                    disabled={!isEditing}
                  />
                </div>
              )}
            </div>

            {isEditing && (
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setActiveTab('overview')}
                >
                  Back
                </Button>
                <Button
                  onClick={() => setActiveTab('timeline')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Next: Timeline & Budget
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            )}
          </motion.div>
        )}

        {/* Timeline & Budget Tab */}
        {activeTab === 'timeline' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="start-date" className="text-gray-700">Start Date</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="completion-date" className="text-gray-700">Completion Date</Label>
                <Input
                  id="completion-date"
                  type="date"
                  value={completionDate}
                  onChange={(e) => setCompletionDate(e.target.value)}
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="estimated-budget" className="text-gray-700">Estimated Budget</Label>
                <Input
                  id="estimated-budget"
                  value={estimatedBudget}
                  onChange={(e) => setEstimatedBudget(e.target.value)}
                  placeholder="Enter estimated budget"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="actual-cost" className="text-gray-700">Actual Cost</Label>
                <Input
                  id="actual-cost"
                  value={actualCost}
                  onChange={(e) => setActualCost(e.target.value)}
                  placeholder="Enter actual cost"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="sale-price" className="text-gray-700">Sale Price</Label>
                <Input
                  id="sale-price"
                  value={salePrice}
                  onChange={(e) => setSalePrice(e.target.value)}
                  placeholder="e.g. $500,000"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
            </div>

            {isEditing && (
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setActiveTab('building')}
                >
                  Back
                </Button>
                <Button
                  onClick={() => setActiveTab('team')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Next: Team
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            )}
          </motion.div>
        )}

        {/* Team Tab */}
        {activeTab === 'team' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="client-name" className="text-gray-700">Client Name</Label>
                <Input
                  id="client-name"
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Enter client name"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
              <div>
                <Label htmlFor="project-manager" className="text-gray-700">Project Manager</Label>
                <Input
                  id="project-manager"
                  value={projectManager}
                  onChange={(e) => setProjectManager(e.target.value)}
                  placeholder="Enter project manager name"
                  className="mt-1.5"
                  disabled={!isEditing}
                />
              </div>
            </div>

            {isEditing && (
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setActiveTab('timeline')}
                >
                  Back
                </Button>
                <Button
                  onClick={() => setActiveTab('files')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Next: Files
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
            )}
          </motion.div>
        )}

        {/* Files Tab */}
        {activeTab === 'files' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Label className="text-gray-700 block">Project Documents</Label>
                <FileUpload
                  onUpload={(files) => setDocuments(files)}
                  acceptedFileTypes=".pdf,.doc,.docx,.xls,.xlsx"
                  multiple={true}
                  buttonText={isEditing ? "Upload Documents" : "Documents Disabled"}
                  buttonVariant="outline"
                />

                {documents.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Selected Documents:</h4>
                    <ul className="space-y-1">
                      {documents.map((file, index) => (
                        <li key={index} className="text-sm text-gray-600">
                          {file.name}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <Label className="text-gray-700 block">Project Photos</Label>
                <FileUpload
                  onUpload={(files) => setPhotos(files)}
                  acceptedFileTypes="image/*"
                  multiple={true}
                  buttonText={isEditing ? "Upload Photos" : "Photos Disabled"}
                  buttonVariant="outline"
                />

                {photos.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Selected Photos:</h4>
                    <ul className="space-y-1">
                      {photos.map((file, index) => (
                        <li key={index} className="text-sm text-gray-600">
                          {file.name}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {isEditing && (
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setActiveTab('team')}
                >
                  Back
                </Button>
                <Button
                  onClick={handleSave}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle2 className="mr-1 h-4 w-4" />
                  Save Project
                </Button>
              </div>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
}
